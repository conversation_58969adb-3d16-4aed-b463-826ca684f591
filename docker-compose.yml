version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: memo_db
      POSTGRES_USER: memo_user
      POSTGRES_PASSWORD: memo_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U memo_user -d memo_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  memo-app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_DATASOURCE_URL=***************************************
      - SPRING_DATASOURCE_USERNAME=memo_user
      - SPRING_DATASOURCE_PASSWORD=memo_password
      - OPENROUTER_API_KEY=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
      - MEMO_API_KEY=m0-rw7KSbtcmeUSyNZtMWllGHcPpLjnE1a4prmQ0u9S
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./uploads:/app/uploads

volumes:
  postgres_data:
