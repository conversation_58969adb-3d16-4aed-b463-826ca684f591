package com.memo.controller;

import com.memo.dto.ChatRequest;
import com.memo.dto.ChatResponse;
import com.memo.entity.Conversation;
import com.memo.service.ChatService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

@RestController
@RequestMapping("/chat")
@CrossOrigin(origins = "*")
public class ChatController {

    @Autowired
    private ChatService chatService;

    @PostMapping("/send")
    public Mono<ResponseEntity<ChatResponse>> sendMessage(@Valid @RequestBody ChatRequest request) {
        return chatService.processChat(request)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.badRequest().build());
    }

    @PostMapping("/conversation")
    public Mono<ResponseEntity<Conversation>> createConversation(
            @RequestParam String userId,
            @RequestParam(defaultValue = "gpt-4") String model) {
        return chatService.createConversation(userId, model)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.badRequest().build());
    }

    @GetMapping("/conversations/{userId}")
    public ResponseEntity<List<Conversation>> getUserConversations(@PathVariable String userId) {
        // This would need to be implemented in ChatService
        return ResponseEntity.ok(List.of());
    }
}
