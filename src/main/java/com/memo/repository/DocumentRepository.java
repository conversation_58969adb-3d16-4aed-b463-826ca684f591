package com.memo.repository;

import com.memo.entity.Document;
import com.memo.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DocumentRepository extends JpaRepository<Document, Long> {
    List<Document> findByUserOrderByUploadedAtDesc(User user);
    List<Document> findByUserAndStatusOrderByUploadedAtDesc(User user, Document.DocumentStatus status);
    List<Document> findByUserAndFileTypeOrderByUploadedAtDesc(User user, String fileType);
}
