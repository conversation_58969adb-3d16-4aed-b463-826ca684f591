package com.memo.repository;

import com.memo.entity.Conversation;
import com.memo.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ConversationRepository extends JpaRepository<Conversation, Long> {
    List<Conversation> findByUserOrderByUpdatedAtDesc(User user);
    List<Conversation> findByUserAndModelUsedOrderByUpdatedAtDesc(User user, String modelUsed);
}
