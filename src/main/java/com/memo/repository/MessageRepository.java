package com.memo.repository;

import com.memo.entity.Conversation;
import com.memo.entity.Message;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MessageRepository extends JpaRepository<Message, Long> {
    List<Message> findByConversationOrderByTimestampAsc(Conversation conversation);
    List<Message> findByConversationAndRoleOrderByTimestampAsc(Conversation conversation, Message.MessageRole role);
}
