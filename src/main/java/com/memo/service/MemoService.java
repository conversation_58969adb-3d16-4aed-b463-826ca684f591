package com.memo.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

@Service
public class MemoService {

    @Autowired
    private WebClient memoWebClient;

    @Autowired
    private ObjectMapper objectMapper;

    public Mono<String> createSession(String userId) {
        ObjectNode requestBody = objectMapper.createObjectNode();
        requestBody.put("user_id", userId);

        return memoWebClient.post()
                .uri("/sessions")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(JsonNode.class)
                .map(response -> response.get("session_id").asText());
    }

    public Mono<String> addMemory(String sessionId, String content, String metadata) {
        ObjectNode requestBody = objectMapper.createObjectNode();
        requestBody.put("session_id", sessionId);
        requestBody.put("content", content);
        if (metadata != null) {
            requestBody.put("metadata", metadata);
        }

        return memoWebClient.post()
                .uri("/memories")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(JsonNode.class)
                .map(response -> response.get("memory_id").asText());
    }

    public Mono<List<Map<String, Object>>> retrieveMemories(String sessionId, String query, int limit) {
        ObjectNode requestBody = objectMapper.createObjectNode();
        requestBody.put("session_id", sessionId);
        requestBody.put("query", query);
        requestBody.put("limit", limit);

        return memoWebClient.post()
                .uri("/memories/retrieve")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(JsonNode.class)
                .map(response -> {
                    JsonNode memories = response.get("memories");
                    if (memories != null && memories.isArray()) {
                        return objectMapper.convertValue(memories, List.class);
                    }
                    return List.of();
                });
    }

    public Mono<Void> deleteMemory(String sessionId, String memoryId) {
        return memoWebClient.delete()
                .uri("/memories/{memoryId}?session_id={sessionId}", memoryId, sessionId)
                .retrieve()
                .bodyToMono(Void.class);
    }

    public Mono<Void> deleteSession(String sessionId) {
        return memoWebClient.delete()
                .uri("/sessions/{sessionId}", sessionId)
                .retrieve()
                .bodyToMono(Void.class);
    }

    public Mono<Map<String, Object>> getSessionInfo(String sessionId) {
        return memoWebClient.get()
                .uri("/sessions/{sessionId}", sessionId)
                .retrieve()
                .bodyToMono(JsonNode.class)
                .map(response -> objectMapper.convertValue(response, Map.class));
    }
}
