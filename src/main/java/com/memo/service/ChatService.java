package com.memo.service;

import com.memo.dto.ChatRequest;
import com.memo.dto.ChatResponse;
import com.memo.entity.Conversation;
import com.memo.entity.Message;
import com.memo.entity.User;
import com.memo.repository.ConversationRepository;
import com.memo.repository.MessageRepository;
import com.memo.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ChatService {

    @Autowired
    private OpenRouterService openRouterService;

    @Autowired
    private MemoService memoService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ConversationRepository conversationRepository;

    @Autowired
    private MessageRepository messageRepository;

    public Mono<ChatResponse> processChat(ChatRequest request) {
        return Mono.fromCallable(() -> {
            // Get or create user
            User user = userRepository.findByUsername(request.getUserId())
                    .orElseGet(() -> {
                        User newUser = new User(request.getUserId(), request.getUserId() + "@memo.ai");
                        return userRepository.save(newUser);
                    });

            // Get conversation
            Conversation conversation = conversationRepository.findById(request.getConversationId())
                    .orElseThrow(() -> new RuntimeException("Conversation not found"));

            // Create or get Mem0 session
            String sessionId = conversation.getMemoSessionId();
            if (sessionId == null) {
                sessionId = memoService.createSession(user.getId().toString()).block();
                conversation.setMemoSessionId(sessionId);
                conversationRepository.save(conversation);
            }

            // Retrieve relevant memories
            List<Map<String, Object>> memories = memoService.retrieveMemories(sessionId, request.getMessage(), 5).block();

            // Build messages for LLM
            List<Map<String, String>> messages = new ArrayList<>();
            
            // Add system message with context
            Map<String, String> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");
            systemMessage.put("content", "You are a helpful AI assistant. Use the provided context to give relevant and accurate responses.");
            messages.add(systemMessage);

            // Add memory context if available
            if (!memories.isEmpty()) {
                StringBuilder contextBuilder = new StringBuilder("Previous relevant context:\n");
                for (Map<String, Object> memory : memories) {
                    contextBuilder.append("- ").append(memory.get("content")).append("\n");
                }
                
                Map<String, String> contextMessage = new HashMap<>();
                contextMessage.put("role", "system");
                contextMessage.put("content", contextBuilder.toString());
                messages.add(contextMessage);
            }

            // Add conversation history
            List<Message> conversationMessages = messageRepository.findByConversationOrderByTimestampAsc(conversation);
            for (Message msg : conversationMessages) {
                Map<String, String> messageMap = new HashMap<>();
                messageMap.put("role", msg.getRole().toString().toLowerCase());
                messageMap.put("content", msg.getContent());
                messages.add(messageMap);
            }

            // Add current user message
            Map<String, String> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", request.getMessage());
            messages.add(userMessage);

            // Generate response
            String model = request.getModel() != null ? request.getModel() : "gpt-4";
            Map<String, Object> responseData = openRouterService.generateResponseWithMetadata(model, messages).block();

            // Save user message
            Message userMsg = new Message(Message.MessageRole.USER, request.getMessage());
            userMsg.setConversation(conversation);
            userMsg.setModelUsed(model);
            messageRepository.save(userMsg);

            // Save assistant response
            Message assistantMsg = new Message(Message.MessageRole.ASSISTANT, (String) responseData.get("response"));
            assistantMsg.setConversation(conversation);
            assistantMsg.setModelUsed(model);
            assistantMsg.setTokensUsed((Integer) responseData.get("tokensUsed"));
            messageRepository.save(assistantMsg);

            // Store in Mem0
            memoService.addMemory(sessionId, request.getMessage(), "user_message").block();
            memoService.addMemory(sessionId, (String) responseData.get("response"), "assistant_response").block();

            // Create response
            ChatResponse response = new ChatResponse();
            response.setResponse((String) responseData.get("response"));
            response.setConversationId(conversation.getId());
            response.setModelUsed(model);
            response.setTokensUsed((Integer) responseData.get("tokensUsed"));
            response.setMemoSessionId(sessionId);

            return response;
        });
    }

    public Mono<Conversation> createConversation(String userId, String model) {
        return Mono.fromCallable(() -> {
            User user = userRepository.findByUsername(userId)
                    .orElseGet(() -> {
                        User newUser = new User(userId, userId + "@memo.ai");
                        return userRepository.save(newUser);
                    });

            Conversation conversation = new Conversation(user, model);
            return conversationRepository.save(conversation);
        });
    }
}
