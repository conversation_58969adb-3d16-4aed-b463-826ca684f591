package com.memo.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

@Service
public class OpenRouterService {

    @Autowired
    private WebClient openRouterWebClient;

    @Autowired
    private ObjectMapper objectMapper;

    public Mono<String> generateResponse(String model, List<Map<String, String>> messages) {
        ObjectNode requestBody = objectMapper.createObjectNode();
        requestBody.put("model", model);
        
        ArrayNode messagesArray = objectMapper.createArrayNode();
        for (Map<String, String> message : messages) {
            ObjectNode messageNode = objectMapper.createObjectNode();
            messageNode.put("role", message.get("role"));
            messageNode.put("content", message.get("content"));
            messagesArray.add(messageNode);
        }
        requestBody.set("messages", messagesArray);

        return openRouterWebClient.post()
                .uri("/chat/completions")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(JsonNode.class)
                .map(response -> {
                    JsonNode choices = response.get("choices");
                    if (choices != null && choices.isArray() && choices.size() > 0) {
                        JsonNode firstChoice = choices.get(0);
                        JsonNode message = firstChoice.get("message");
                        if (message != null) {
                            return message.get("content").asText();
                        }
                    }
                    throw new RuntimeException("Invalid response format from OpenRouter");
                });
    }

    public Mono<Map<String, Object>> generateResponseWithMetadata(String model, List<Map<String, String>> messages) {
        ObjectNode requestBody = objectMapper.createObjectNode();
        requestBody.put("model", model);
        
        ArrayNode messagesArray = objectMapper.createArrayNode();
        for (Map<String, String> message : messages) {
            ObjectNode messageNode = objectMapper.createObjectNode();
            messageNode.put("role", message.get("role"));
            messageNode.put("content", message.get("content"));
            messagesArray.add(messageNode);
        }
        requestBody.set("messages", messagesArray);

        return openRouterWebClient.post()
                .uri("/chat/completions")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(JsonNode.class)
                .map(response -> {
                    JsonNode choices = response.get("choices");
                    JsonNode usage = response.get("usage");
                    
                    if (choices != null && choices.isArray() && choices.size() > 0) {
                        JsonNode firstChoice = choices.get(0);
                        JsonNode message = firstChoice.get("message");
                        if (message != null) {
                            String content = message.get("content").asText();
                            
                            return Map.of(
                                "response", content,
                                "model", model,
                                "tokensUsed", usage != null ? usage.get("total_tokens").asInt() : 0
                            );
                        }
                    }
                    throw new RuntimeException("Invalid response format from OpenRouter");
                });
    }

    public Mono<List<String>> getAvailableModels() {
        return openRouterWebClient.get()
                .uri("/models")
                .retrieve()
                .bodyToMono(JsonNode.class)
                .map(response -> {
                    JsonNode data = response.get("data");
                    if (data != null && data.isArray()) {
                        return objectMapper.convertValue(data, List.class);
                    }
                    return List.of();
                });
    }
}
