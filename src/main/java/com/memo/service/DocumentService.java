package com.memo.service;

import com.memo.entity.Document;
import com.memo.entity.User;
import com.memo.repository.DocumentRepository;
import com.memo.repository.UserRepository;
import org.apache.commons.io.FilenameUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
public class DocumentService {

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private UserRepository userRepository;

    @Value("${document.storage.path}")
    private String storagePath;

    public Document processDocument(MultipartFile file, String userId) throws IOException {
        // Get or create user
        User user = userRepository.findByUsername(userId)
                .orElseGet(() -> {
                    User newUser = new User(userId, userId + "@memo.ai");
                    return userRepository.save(newUser);
                });

        // Generate unique filename
        String originalFilename = file.getOriginalFilename();
        String fileExtension = FilenameUtils.getExtension(originalFilename);
        String uniqueFilename = UUID.randomUUID().toString() + "." + fileExtension;

        // Create storage directory if it doesn't exist
        Path storageDir = Paths.get(storagePath);
        if (!Files.exists(storageDir)) {
            Files.createDirectories(storageDir);
        }

        // Save file to storage
        Path filePath = storageDir.resolve(uniqueFilename);
        Files.copy(file.getInputStream(), filePath);

        // Create document entity
        Document document = new Document(user, uniqueFilename, originalFilename, fileExtension, file.getSize());
        document.setFilePath(filePath.toString());
        document.setStatus(Document.DocumentStatus.PROCESSING);

        // Save to database
        document = documentRepository.save(document);

        // Process document content asynchronously
        processDocumentContent(document);

        return document;
    }

    private void processDocumentContent(Document document) {
        try {
            String content = extractTextFromDocument(document);
            document.setContent(content);
            document.setStatus(Document.DocumentStatus.PROCESSED);
            document.setProcessedAt(LocalDateTime.now());
            documentRepository.save(document);
        } catch (Exception e) {
            document.setStatus(Document.DocumentStatus.FAILED);
            documentRepository.save(document);
        }
    }

    private String extractTextFromDocument(Document document) throws IOException {
        String filePath = document.getFilePath();
        String fileType = document.getFileType().toLowerCase();

        switch (fileType) {
            case "pdf":
                return extractTextFromPdf(filePath);
            case "docx":
                return extractTextFromDocx(filePath);
            case "txt":
                return extractTextFromTxt(filePath);
            default:
                throw new UnsupportedOperationException("Unsupported file type: " + fileType);
        }
    }

    private String extractTextFromPdf(String filePath) throws IOException {
        try (PDDocument document = PDDocument.load(new File(filePath))) {
            PDFTextStripper stripper = new PDFTextStripper();
            return stripper.getText(document);
        }
    }

    private String extractTextFromDocx(String filePath) throws IOException {
        try (XWPFDocument document = new XWPFDocument(Files.newInputStream(Paths.get(filePath)))) {
            StringBuilder content = new StringBuilder();
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                content.append(paragraph.getText()).append("\n");
            }
            return content.toString();
        }
    }

    private String extractTextFromTxt(String filePath) throws IOException {
        return Files.readString(Paths.get(filePath));
    }

    public List<Document> getUserDocuments(String userId) {
        User user = userRepository.findByUsername(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        return documentRepository.findByUserOrderByUploadedAtDesc(user);
    }

    public Document getDocument(Long documentId) {
        return documentRepository.findById(documentId).orElse(null);
    }

    public void deleteDocument(Long documentId) {
        Document document = documentRepository.findById(documentId)
                .orElseThrow(() -> new RuntimeException("Document not found"));

        // Delete file from storage
        try {
            Files.deleteIfExists(Paths.get(document.getFilePath()));
        } catch (IOException e) {
            // Log error but continue with database deletion
        }

        documentRepository.delete(document);
    }

    public Document reprocessDocument(Long documentId) {
        Document document = documentRepository.findById(documentId)
                .orElseThrow(() -> new RuntimeException("Document not found"));

        document.setStatus(Document.DocumentStatus.PROCESSING);
        documentRepository.save(document);

        processDocumentContent(document);
        return document;
    }
}
