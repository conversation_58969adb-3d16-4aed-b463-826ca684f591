package com.memo.dto;

import java.time.LocalDateTime;

public class ChatResponse {

    private String response;
    private Long conversationId;
    private String modelUsed;
    private Integer tokensUsed;
    private LocalDateTime timestamp;
    private String memoSessionId;

    // Constructors
    public ChatResponse() {
        this.timestamp = LocalDateTime.now();
    }

    public ChatResponse(String response, Long conversationId, String modelUsed) {
        this();
        this.response = response;
        this.conversationId = conversationId;
        this.modelUsed = modelUsed;
    }

    // Getters and Setters
    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public Long getConversationId() {
        return conversationId;
    }

    public void setConversationId(Long conversationId) {
        this.conversationId = conversationId;
    }

    public String getModelUsed() {
        return modelUsed;
    }

    public void setModelUsed(String modelUsed) {
        this.modelUsed = modelUsed;
    }

    public Integer getTokensUsed() {
        return tokensUsed;
    }

    public void setTokensUsed(Integer tokensUsed) {
        this.tokensUsed = tokensUsed;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getMemoSessionId() {
        return memoSessionId;
    }

    public void setMemoSessionId(String memoSessionId) {
        this.memoSessionId = memoSessionId;
    }
}
