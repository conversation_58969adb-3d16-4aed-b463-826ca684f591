spring:
  application:
    name: memo-ai
  
  datasource:
    url: ****************************************
    username: memo_user
    password: memo_password
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
  
  security:
    user:
      name: admin
      password: admin123

# OpenRouter Configuration
openrouter:
  api-key: 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
  base-url: https://openrouter.ai/api/v1
  default-model: gpt-4
  models:
    gpt-4: gpt-4
    gpt-3.5-turbo: gpt-3.5-turbo
    claude-3: anthropic/claude-3-sonnet
    llama-2: meta-llama/llama-2-70b-chat

# Mem0 Configuration
memo:
  api-key: m0-rw7KSbtcmeUSyNZtMWllGHcPpLjnE1a4prmQ0u9S
  base-url: https://api.memo.ai
  memory-retention-days: 30
  max-context-length: 4000

# Document Processing
document:
  upload:
    max-size: 10MB
    allowed-types: pdf,txt,docx,jpg,jpeg,png
  storage:
    path: ./uploads
    cleanup-enabled: true
    cleanup-interval: 86400 # 24 hours

# Server Configuration
server:
  port: 8080
  servlet:
    context-path: /api

# Logging
logging:
  level:
    com.memo: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
