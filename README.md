# MemO AI - Java Spring Boot Application

A Java 17 Spring Boot application that replaces <PERSON><PERSON><PERSON><PERSON> with Mem0 for memory management, uses OpenRouter for LLM access, and PostgreSQL for persistent storage of user context and conversation history across multiple sessions.

## 🚀 Features

### Core Features
- **Multi-Model Support**: Access to GPT-4, <PERSON>, <PERSON><PERSON><PERSON>, and other models via OpenRouter
- **Persistent Memory**: Mem0 integration for context-aware conversations
- **Document Processing**: Support for PDF, TXT, DOCX, and image files
- **RAG Support**: Retrieval Augmented Generation with document embeddings
- **User Management**: Isolated user sessions and conversation history
- **RESTful API**: Complete API for chat, document management, and memory operations

### Technology Stack
- **Language**: Java 17
- **Framework**: Spring Boot 3.2.0
- **Memory Management**: Mem0 API
- **LLM Provider**: OpenRouter API
- **Database**: PostgreSQL
- **Build Tool**: Maven

## 📋 Prerequisites

- Java 17 or higher
- Maven 3.6+
- PostgreSQL 12+
- OpenRouter API key
- Mem0 API key

## 🛠️ Setup Instructions

### 1. Database Setup

Create a PostgreSQL database:

```sql
CREATE DATABASE memo_db;
CREATE USER memo_user WITH PASSWORD 'memo_password';
GRANT ALL PRIVILEGES ON DATABASE memo_db TO memo_user;
```

### 2. Environment Configuration

Create a `.env` file in the project root:

```bash
# OpenRouter Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Mem0 Configuration
MEMO_API_KEY=your_memo_api_key_here

# Database Configuration (optional, defaults in application.yml)
DB_URL=****************************************
DB_USERNAME=memo_user
DB_PASSWORD=memo_password
```

### 3. Build and Run

```bash
# Clone the repository
git clone <repository-url>
cd MemO

# Build the project
mvn clean install

# Run the application
mvn spring-boot:run
```

The application will start on `http://localhost:8080`

## 📚 API Documentation

### Chat Endpoints

#### Send Message
```http
POST /api/chat/send
Content-Type: application/json

{
  "message": "Hello, how are you?",
  "conversationId": 1,
  "model": "gpt-4",
  "userId": "user123"
}
```

#### Create Conversation
```http
POST /api/chat/conversation?userId=user123&model=gpt-4
```

#### Get User Conversations
```http
GET /api/chat/conversations/{userId}
```

### Document Endpoints

#### Upload Document
```http
POST /api/documents/upload
Content-Type: multipart/form-data

file: [document file]
userId: user123
```

#### Get User Documents
```http
GET /api/documents/user/{userId}
```

#### Get Document
```http
GET /api/documents/{documentId}
```

#### Delete Document
```http
DELETE /api/documents/{documentId}
```

#### Reprocess Document
```http
POST /api/documents/{documentId}/process
```

## 🏗️ Project Structure

```
src/main/java/com/memo/
├── MemoApplication.java          # Main application class
├── config/                       # Configuration classes
│   ├── OpenRouterConfig.java     # OpenRouter API configuration
│   ├── MemoConfig.java          # Mem0 API configuration
│   ├── SecurityConfig.java      # Spring Security configuration
│   └── WebConfig.java           # Web configuration
├── controller/                   # REST controllers
│   ├── ChatController.java      # Chat endpoints
│   └── DocumentController.java  # Document endpoints
├── dto/                         # Data Transfer Objects
│   ├── ChatRequest.java         # Chat request DTO
│   └── ChatResponse.java        # Chat response DTO
├── entity/                      # JPA entities
│   ├── User.java               # User entity
│   ├── Conversation.java       # Conversation entity
│   ├── Message.java            # Message entity
│   └── Document.java           # Document entity
├── repository/                  # JPA repositories
│   ├── UserRepository.java     # User repository
│   ├── ConversationRepository.java
│   ├── MessageRepository.java
│   └── DocumentRepository.java
├── service/                     # Business logic services
│   ├── ChatService.java        # Main chat orchestration
│   ├── OpenRouterService.java  # OpenRouter API integration
│   ├── MemoService.java        # Mem0 API integration
│   └── DocumentService.java    # Document processing
└── util/                        # Utility classes
    └── FileUtils.java          # File validation utilities
```

## 🔧 Configuration

### Application Properties

Key configuration options in `application.yml`:

```yaml
# OpenRouter Configuration
openrouter:
  api-key: ${OPENROUTER_API_KEY}
  base-url: https://openrouter.ai/api/v1
  default-model: gpt-4
  models:
    gpt-4: gpt-4
    gpt-3.5-turbo: gpt-3.5-turbo
    claude-3: anthropic/claude-3-sonnet
    llama-2: meta-llama/llama-2-70b-chat

# Mem0 Configuration
memo:
  api-key: ${MEMO_API_KEY}
  base-url: https://api.memo.ai
  memory-retention-days: 30
  max-context-length: 4000

# Document Processing
document:
  upload:
    max-size: 10MB
    allowed-types: pdf,txt,docx,jpg,jpeg,png
  storage:
    path: ./uploads
```

## 🧪 Testing

Run the test suite:

```bash
mvn test
```

## 📦 Deployment

### Docker Deployment

Create a `Dockerfile`:

```dockerfile
FROM openjdk:17-jdk-slim
COPY target/memo-ai-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

Build and run:

```bash
docker build -t memo-ai .
docker run -p 8080:8080 memo-ai
```

### Production Deployment

1. Set up a PostgreSQL database
2. Configure environment variables
3. Build the JAR file: `mvn clean package`
4. Run: `java -jar target/memo-ai-1.0.0.jar`

## 🔒 Security

- Basic authentication enabled
- CORS configured for cross-origin requests
- File upload validation and size limits
- SQL injection protection via JPA
- Input validation on all endpoints

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:
- Create an issue in the repository
- Check the API documentation
- Review the configuration examples

## 🔄 Changelog

### Version 1.0.0
- Initial release
- OpenRouter integration
- Mem0 memory management
- Document processing
- RESTful API
- PostgreSQL persistence